<?php

namespace App\Observers\Administration\Penalty;

use App\Models\Penalty\Penalty;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class PenaltyObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Penalty "created" event.
     */
    public function created(Penalty $penalty): void
    {
        //
    }

    /**
     * Handle the Penalty "updated" event.
     */
    public function updated(Penalty $penalty): void
    {
        //
    }

    /**
     * Handle the Penalty "deleted" event.
     */
    public function deleted(Penalty $penalty): void
    {
        //
    }

    /**
     * Handle the Penalty "restored" event.
     */
    public function restored(Penalty $penalty): void
    {
        //
    }

    /**
     * Handle the Penalty "force deleted" event.
     */
    public function forceDeleted(Penalty $penalty): void
    {
        //
    }
}
