<?php

namespace App\Observers\Administration\Penalty;

use App\Models\User;
use App\Models\Penalty\Penalty;
use Illuminate\Support\Facades\Mail;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use App\Mail\Administration\Penalty\PenaltyCreatedMail;
use App\Notifications\Administration\Penalty\PenaltyCreatedNotification;

class PenaltyObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Penalty "created" event.
     */
    public function created(Penalty $penalty): void
    {
        // Load necessary relationships for notifications
        $penalty->load(['user.employee', 'attendance', 'creator.employee']);

        // Send notifications automatically when a penalty is created
        $this->sendPenaltyNotifications($penalty);
    }

    /**
     * Handle the Penalty "updated" event.
     */
    public function updated(Penalty $penalty): void
    {
        //
    }

    /**
     * Handle the Penalty "deleted" event.
     */
    public function deleted(Penalty $penalty): void
    {
        //
    }

    /**
     * Handle the Penalty "restored" event.
     */
    public function restored(Penalty $penalty): void
    {
        //
    }

    /**
     * Handle the Penalty "force deleted" event.
     */
    public function forceDeleted(Penalty $penalty): void
    {
        //
    }

    /**
     * Send penalty notifications to employee and team leader
     */
    private function sendPenaltyNotifications(Penalty $penalty): void
    {
        $employee = $penalty->user;
        $creator = auth()->user();

        // 1. Notify the employee who received the penalty
        if ($employee && $employee->employee && $employee->employee->official_email) {
            // Send in-app notification
            $employee->notify(new PenaltyCreatedNotification($penalty, $creator));

            // Send email notification
            Mail::to($employee->employee->official_email)
                ->queue(new PenaltyCreatedMail($penalty, $employee));
        }

        // 2. Notify the employee's active team leader
        $activeTeamLeader = $this->getActiveTeamLeader($employee);

        if ($activeTeamLeader && $activeTeamLeader->employee && $activeTeamLeader->employee->official_email) {
            // Send in-app notification (with team member context)
            $activeTeamLeader->notify(new PenaltyCreatedNotification($penalty, $creator, true));

            // Send email notification
            Mail::to($activeTeamLeader->employee->official_email)
                ->queue(new PenaltyCreatedMail($penalty, $activeTeamLeader));
        }
    }

    /**
     * Get the active team leader for an employee
     */
    private function getActiveTeamLeader(User $employee): ?User
    {
        return $employee->employee_team_leaders()
            ->wherePivot('is_active', true)
            ->with(['employee'])
            ->first();
    }
}
