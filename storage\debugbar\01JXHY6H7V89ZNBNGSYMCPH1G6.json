{"__meta": {"id": "01JXHY6H7V89ZNBNGSYMCPH1G6", "datetime": "2025-06-12 17:25:28", "utime": **********.189128, "method": "GET", "uri": "/announcement/show/w0m8nb5By7WPvjad", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749727521.040882, "end": **********.189164, "duration": 7.148281812667847, "duration_str": "7.15s", "measures": [{"label": "Booting", "start": 1749727521.040882, "relative_start": 0, "end": **********.245773, "relative_end": **********.245773, "duration": 1.****************, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.245797, "relative_start": 1.****************, "end": **********.189168, "relative_end": 4.0531158447265625e-06, "duration": 5.***************, "duration_str": "5.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.280757, "relative_start": 1.****************, "end": **********.295558, "relative_end": **********.295558, "duration": 0.***************, "duration_str": "14.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.939003, "relative_start": 1.****************, "end": **********.183599, "relative_end": **********.183599, "duration": 5.***************, "duration_str": "5.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 25, "nb_templates": 25, "templates": [{"name": "1x administration.announcement.show", "param_count": null, "params": [], "start": **********.947849, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/announcement/show.blade.phpadministration.announcement.show", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Fannouncement%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.announcement.show"}, {"name": "1x layouts.administration.app", "param_count": null, "params": [], "start": **********.003145, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/app.blade.phplayouts.administration.app", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.app"}, {"name": "1x layouts.administration.partials.metas", "param_count": null, "params": [], "start": **********.004724, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/metas.blade.phplayouts.administration.partials.metas", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmetas.blade.php&line=1", "ajax": false, "filename": "metas.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.metas"}, {"name": "1x layouts.administration.partials.stylesheet", "param_count": null, "params": [], "start": **********.005688, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/stylesheet.blade.phplayouts.administration.partials.stylesheet", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fstylesheet.blade.php&line=1", "ajax": false, "filename": "stylesheet.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.stylesheet"}, {"name": "1x layouts.administration.partials.sidenav", "param_count": null, "params": [], "start": **********.00661, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/sidenav.blade.phplayouts.administration.partials.sidenav", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fsidenav.blade.php&line=1", "ajax": false, "filename": "sidenav.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.sidenav"}, {"name": "1x layouts.administration.partials.menus.dashboard", "param_count": null, "params": [], "start": **********.007911, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/dashboard.blade.phplayouts.administration.partials.menus.dashboard", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.dashboard"}, {"name": "1x layouts.administration.partials.menus.chatting", "param_count": null, "params": [], "start": **********.008971, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/chatting.blade.phplayouts.administration.partials.menus.chatting", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fchatting.blade.php&line=1", "ajax": false, "filename": "chatting.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.chatting"}, {"name": "1x layouts.administration.partials.menus.vault", "param_count": null, "params": [], "start": **********.016751, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/vault.blade.phplayouts.administration.partials.menus.vault", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fvault.blade.php&line=1", "ajax": false, "filename": "vault.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.vault"}, {"name": "1x layouts.administration.partials.menus.attendance", "param_count": null, "params": [], "start": **********.022404, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/attendance.blade.phplayouts.administration.partials.menus.attendance", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fattendance.blade.php&line=1", "ajax": false, "filename": "attendance.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.attendance"}, {"name": "1x layouts.administration.partials.menus.daily_break", "param_count": null, "params": [], "start": **********.040248, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/daily_break.blade.phplayouts.administration.partials.menus.daily_break", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fdaily_break.blade.php&line=1", "ajax": false, "filename": "daily_break.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.daily_break"}, {"name": "1x layouts.administration.partials.menus.leave", "param_count": null, "params": [], "start": **********.045064, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/leave.blade.phplayouts.administration.partials.menus.leave", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fleave.blade.php&line=1", "ajax": false, "filename": "leave.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.leave"}, {"name": "1x layouts.administration.partials.menus.penalty", "param_count": null, "params": [], "start": **********.057677, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/penalty.blade.phplayouts.administration.partials.menus.penalty", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fpenalty.blade.php&line=1", "ajax": false, "filename": "penalty.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.penalty"}, {"name": "1x layouts.administration.partials.menus.announcement", "param_count": null, "params": [], "start": **********.072573, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/announcement.blade.phplayouts.administration.partials.menus.announcement", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fannouncement.blade.php&line=1", "ajax": false, "filename": "announcement.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.announcement"}, {"name": "1x layouts.administration.partials.menus.task", "param_count": null, "params": [], "start": **********.077198, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/task.blade.phplayouts.administration.partials.menus.task", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Ftask.blade.php&line=1", "ajax": false, "filename": "task.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.task"}, {"name": "1x layouts.administration.partials.menus.daily_work_update", "param_count": null, "params": [], "start": **********.085241, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/daily_work_update.blade.phplayouts.administration.partials.menus.daily_work_update", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fdaily_work_update.blade.php&line=1", "ajax": false, "filename": "daily_work_update.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.daily_work_update"}, {"name": "1x layouts.administration.partials.menus.it_ticket", "param_count": null, "params": [], "start": **********.09106, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/it_ticket.blade.phplayouts.administration.partials.menus.it_ticket", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fit_ticket.blade.php&line=1", "ajax": false, "filename": "it_ticket.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.it_ticket"}, {"name": "1x layouts.administration.partials.menus.booking", "param_count": null, "params": [], "start": **********.096779, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/booking.blade.phplayouts.administration.partials.menus.booking", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fbooking.blade.php&line=1", "ajax": false, "filename": "booking.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.booking"}, {"name": "1x layouts.administration.partials.menus.salary", "param_count": null, "params": [], "start": **********.103599, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/salary.blade.phplayouts.administration.partials.menus.salary", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fsalary.blade.php&line=1", "ajax": false, "filename": "salary.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.salary"}, {"name": "1x layouts.administration.partials.menus.income_expense", "param_count": null, "params": [], "start": **********.105538, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/income_expense.blade.phplayouts.administration.partials.menus.income_expense", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fincome_expense.blade.php&line=1", "ajax": false, "filename": "income_expense.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.income_expense"}, {"name": "1x layouts.administration.partials.menus.logs", "param_count": null, "params": [], "start": **********.120304, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/logs.blade.phplayouts.administration.partials.menus.logs", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Flogs.blade.php&line=1", "ajax": false, "filename": "logs.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.logs"}, {"name": "1x layouts.administration.partials.menus.settings", "param_count": null, "params": [], "start": **********.123738, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/settings.blade.phplayouts.administration.partials.menus.settings", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fsettings.blade.php&line=1", "ajax": false, "filename": "settings.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.settings"}, {"name": "1x layouts.administration.partials.topnav", "param_count": null, "params": [], "start": **********.149845, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.phplayouts.administration.partials.topnav", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=1", "ajax": false, "filename": "topnav.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.topnav"}, {"name": "1x layouts.administration.partials.breadcrumb", "param_count": null, "params": [], "start": **********.177499, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/breadcrumb.blade.phplayouts.administration.partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.breadcrumb"}, {"name": "1x layouts.administration.partials.scripts", "param_count": null, "params": [], "start": **********.178568, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/scripts.blade.phplayouts.administration.partials.scripts", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.scripts"}, {"name": "1x sweetalert::alert", "param_count": null, "params": [], "start": **********.180975, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "sweetalert::alert"}]}, "route": {"uri": "GET announcement/show/{announcement}", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Announcement Read", "controller": "App\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController@show<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=146\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.announcement.show", "prefix": "/announcement", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=146\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Announcement/AnnouncementController.php:146-189</a>"}, "queries": {"count": 60, "nb_statements": 60, "nb_visible_statements": 60, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10640999999999998, "accumulated_duration_str": "106ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.3946042, "duration": 0.00536, "duration_str": "5.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 5.037}, {"sql": "select * from `announcements` where `id` = 3 and `announcements`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Traits/HasCustomRouteId.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Traits\\HasCustomRouteId.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 959}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php", "line": 214}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}], "start": **********.414332, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "HasCustomRouteId.php:19", "source": {"index": 17, "namespace": null, "name": "app/Traits/HasCustomRouteId.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Traits\\HasCustomRouteId.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FTraits%2FHasCustomRouteId.php&line=19", "ajax": false, "filename": "HasCustomRouteId.php", "line": "19"}, "connection": "blueorange", "explain": null, "start_percent": 5.037, "width_percent": 1.372}, {"sql": "select * from `users` where `users`.`id` in (1) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/HasCustomRouteId.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Traits\\HasCustomRouteId.php", "line": 19}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 959}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php", "line": 214}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}], "start": **********.425444, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "HasCustomRouteId.php:19", "source": {"index": 22, "namespace": null, "name": "app/Traits/HasCustomRouteId.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Traits\\HasCustomRouteId.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FTraits%2FHasCustomRouteId.php&line=19", "ajax": false, "filename": "HasCustomRouteId.php", "line": "19"}, "connection": "blueorange", "explain": null, "start_percent": 6.409, "width_percent": 1.062}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4349492, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 7.471, "width_percent": 0.959}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4812372, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 8.43, "width_percent": 1.569}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.486739, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "blueorange", "explain": null, "start_percent": 9.999, "width_percent": 1.541}, {"sql": "update `announcements` set `read_by_at` = '\\\"[{\\\\\\\"read_by\\\\\\\":1,\\\\\\\"read_at\\\\\\\":\\\\\\\"2025-06-12 17:25:22\\\\\\\"}]\\\"', `announcements`.`updated_at` = '2025-06-12 17:25:22' where `id` = 3", "type": "query", "params": [], "bindings": ["\"[{\\\"read_by\\\":1,\\\"read_at\\\":\\\"2025-06-12 17:25:22\\\"}]\"", "2025-06-12 17:25:22", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 170}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.504602, "duration": 0.01638, "duration_str": "16.38ms", "memory": 0, "memory_str": null, "filename": "AnnouncementController.php:170", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 170}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=170", "ajax": false, "filename": "AnnouncementController.php", "line": "170"}, "connection": "blueorange", "explain": null, "start_percent": 11.54, "width_percent": 15.393}, {"sql": "select * from `announcements` where `id` = 3 and `announcements`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.526448, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "AnnouncementController.php:186", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=186", "ajax": false, "filename": "AnnouncementController.php", "line": "186"}, "connection": "blueorange", "explain": null, "start_percent": 26.934, "width_percent": 0.912}, {"sql": "select * from `users` where `users`.`id` in (1) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5300741, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "AnnouncementController.php:186", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=186", "ajax": false, "filename": "AnnouncementController.php", "line": "186"}, "connection": "blueorange", "explain": null, "start_percent": 27.845, "width_percent": 0.789}, {"sql": "select * from `employees` where `employees`.`user_id` in (1) and `employees`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.539257, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "AnnouncementController.php:186", "source": {"index": 27, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=186", "ajax": false, "filename": "AnnouncementController.php", "line": "186"}, "connection": "blueorange", "explain": null, "start_percent": 28.635, "width_percent": 0.977}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.545989, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "AnnouncementController.php:186", "source": {"index": 27, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=186", "ajax": false, "filename": "AnnouncementController.php", "line": "186"}, "connection": "blueorange", "explain": null, "start_percent": 29.612, "width_percent": 0.921}, {"sql": "select * from `file_media` where `file_media`.`fileable_id` in (3) and `file_media`.`fileable_type` = 'App\\\\Models\\\\Announcement\\\\Announcement' and `file_media`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Announcement\\Announcement"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.550848, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "AnnouncementController.php:186", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=186", "ajax": false, "filename": "AnnouncementController.php", "line": "186"}, "connection": "blueorange", "explain": null, "start_percent": 30.533, "width_percent": 2.067}, {"sql": "select * from `comments` where `comments`.`commentable_id` in (3) and `comments`.`commentable_type` = 'App\\\\Models\\\\Announcement\\\\Announcement' and `comments`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\Announcement\\Announcement"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 182}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9145179, "duration": 0.01585, "duration_str": "15.85ms", "memory": 0, "memory_str": null, "filename": "AnnouncementController.php:182", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 182}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=182", "ajax": false, "filename": "AnnouncementController.php", "line": "182"}, "connection": "blueorange", "explain": null, "start_percent": 32.6, "width_percent": 14.895}, {"sql": "select * from `comments` where `comments`.`commentable_id` in (3) and `comments`.`commentable_type` = 'App\\\\Models\\\\Announcement\\\\Announcement' and `comments`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\Announcement\\Announcement"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.932697, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "AnnouncementController.php:186", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Announcement/AnnouncementController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=186", "ajax": false, "filename": "AnnouncementController.php", "line": "186"}, "connection": "blueorange", "explain": null, "start_percent": 47.496, "width_percent": 0.996}, {"sql": "select `name` from `users` where `id` = 1 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.6220472, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 48.492, "width_percent": 1.081}, {"sql": "select `name` from `users` where `id` = 2 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.626605, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 49.572, "width_percent": 0.808}, {"sql": "select `name` from `users` where `id` = 41 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.630008, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 50.381, "width_percent": 3.233}, {"sql": "select `name` from `users` where `id` = 43 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.636065, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 53.613, "width_percent": 0.818}, {"sql": "select `name` from `users` where `id` = 113 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [113], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.639386, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 54.431, "width_percent": 0.827}, {"sql": "select `name` from `users` where `id` = 72 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [72], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.6429582, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 55.258, "width_percent": 0.912}, {"sql": "select `name` from `users` where `id` = 25 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.646654, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 56.17, "width_percent": 1.889}, {"sql": "select `name` from `users` where `id` = 88 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.65164, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 58.058, "width_percent": 0.827}, {"sql": "select `name` from `users` where `id` = 100 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [100], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.6550682, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 58.885, "width_percent": 0.771}, {"sql": "select `name` from `users` where `id` = 87 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.658595, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 59.656, "width_percent": 0.78}, {"sql": "select `name` from `users` where `id` = 19 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.6621, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 60.436, "width_percent": 0.799}, {"sql": "select `name` from `users` where `id` = 112 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [112], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.666955, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 61.235, "width_percent": 0.865}, {"sql": "select `name` from `users` where `id` = 78 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [78], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.670408, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 62.099, "width_percent": 0.808}, {"sql": "select `name` from `users` where `id` = 86 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [86], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.673726, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 62.908, "width_percent": 0.836}, {"sql": "select `name` from `users` where `id` = 31 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.677072, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 63.744, "width_percent": 0.789}, {"sql": "select `name` from `users` where `id` = 108 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [108], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.680541, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 64.533, "width_percent": 2.199}, {"sql": "select `name` from `users` where `id` = 104 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.6857152, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 66.732, "width_percent": 0.912}, {"sql": "select `name` from `users` where `id` = 116 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [116], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.689157, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 67.644, "width_percent": 0.874}, {"sql": "select `name` from `users` where `id` = 26 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.692569, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 68.518, "width_percent": 0.921}, {"sql": "select `name` from `users` where `id` = 117 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [117], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.6961381, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 69.439, "width_percent": 1.118}, {"sql": "select `name` from `users` where `id` = 17 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.701531, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 70.557, "width_percent": 0.959}, {"sql": "select `name` from `users` where `id` = 37 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.705064, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 71.516, "width_percent": 0.865}, {"sql": "select `name` from `users` where `id` = 79 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [79], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.708592, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 72.38, "width_percent": 0.827}, {"sql": "select `name` from `users` where `id` = 124 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [124], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.7122312, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 73.207, "width_percent": 0.93}, {"sql": "select `name` from `users` where `id` = 33 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.718383, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 74.138, "width_percent": 0.827}, {"sql": "select `name` from `users` where `id` = 45 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.721781, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 74.965, "width_percent": 0.836}, {"sql": "select `name` from `users` where `id` = 13 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.724992, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 75.801, "width_percent": 0.789}, {"sql": "select `name` from `users` where `id` = 36 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.728269, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 76.591, "width_percent": 0.818}, {"sql": "select `name` from `users` where `id` = 10 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.731885, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 77.408, "width_percent": 1.795}, {"sql": "select `name` from `users` where `id` = 23 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.7363071, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 79.203, "width_percent": 0.789}, {"sql": "select `name` from `users` where `id` = 4 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.739359, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 79.992, "width_percent": 0.724}, {"sql": "select `name` from `users` where `id` = 7 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.742575, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 80.716, "width_percent": 0.799}, {"sql": "select `name` from `users` where `id` = 70 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [70], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.74594, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 81.515, "width_percent": 0.836}, {"sql": "select `name` from `users` where `id` = 77 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [77], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.750955, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 82.351, "width_percent": 0.78}, {"sql": "select `name` from `users` where `id` = 15 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.754426, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 83.131, "width_percent": 0.836}, {"sql": "select `name` from `users` where `id` = 16 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.7579198, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 83.968, "width_percent": 0.893}, {"sql": "select `name` from `users` where `id` = 9 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.761398, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 84.86, "width_percent": 0.827}, {"sql": "select `name` from `users` where `id` = 96 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.76532, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 85.687, "width_percent": 2.096}, {"sql": "select `name` from `users` where `id` = 122 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [122], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.770175, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 87.783, "width_percent": 0.93}, {"sql": "select `name` from `users` where `id` = 111 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [111], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727523.773675, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "UserHelper.php:35", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=35", "ajax": false, "filename": "UserHelper.php", "line": "35"}, "connection": "blueorange", "explain": null, "start_percent": 88.713, "width_percent": 0.789}, {"sql": "select `alias_name` from `employees` where `user_id` = 1 and `employees`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1749727527.9986012, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "UserHelper.php:52", "source": {"index": 14, "namespace": null, "name": "app/Helpers/UserHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\UserHelper.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FUserHelper.php&line=52", "ajax": false, "filename": "UserHelper.php", "line": "52"}, "connection": "blueorange", "explain": null, "start_percent": 89.503, "width_percent": 1.41}, {"sql": "select count(*) as aggregate from `chattings` where `receiver_id` = 1 and `seen_at` is null and `chattings`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/ChattingHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\ChattingHelper.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.011105, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "ChattingHelper.php:61", "source": {"index": 16, "namespace": null, "name": "app/Helpers/ChattingHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\ChattingHelper.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FChattingHelper.php&line=61", "ajax": false, "filename": "ChattingHelper.php", "line": "61"}, "connection": "blueorange", "explain": null, "start_percent": 90.913, "width_percent": 1.786}, {"sql": "select * from `shortcuts` where `shortcuts`.`user_id` = 1 and `shortcuts`.`user_id` is not null and `shortcuts`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 67}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.15352, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "layouts.administration.partials.topnav:67", "source": {"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=67", "ajax": false, "filename": "topnav.blade.php", "line": "67"}, "connection": "blueorange", "explain": null, "start_percent": 92.698, "width_percent": 1.081}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.157081, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "layouts.administration.partials.topnav:88", "source": {"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=88", "ajax": false, "filename": "topnav.blade.php", "line": "88"}, "connection": "blueorange", "explain": null, "start_percent": 93.779, "width_percent": 3.815}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 158}], "start": **********.169322, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 97.594, "width_percent": 1.325}, {"sql": "select * from `employees` where `employees`.`user_id` = 1 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 185}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.173952, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "layouts.administration.partials.topnav:185", "source": {"index": 21, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=185", "ajax": false, "filename": "topnav.blade.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 98.919, "width_percent": 1.081}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Announcement\\Announcement": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAnnouncement%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}, "App\\Models\\User\\Employee\\Employee": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FEmployee%2FEmployee.php&line=1", "ajax": false, "filename": "Employee.php", "line": "?"}}, "App\\Models\\Settings\\Settings": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Illuminate\\Notifications\\DatabaseNotification": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2FDatabaseNotification.php&line=1", "ajax": false, "filename": "DatabaseNotification.php", "line": "?"}}}, "count": 10, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 71, "messages": [{"message": "[\n  ability => Announcement Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-927528992 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Announcement Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927528992\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.501157, "xdebug_link": null}, {"message": "[\n  ability => Announcement Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-131821719 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Announcement Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131821719\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749727523.777855, "xdebug_link": null}, {"message": "[\n  ability => Vault Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2003610165 data-indent-pad=\"  \"><span class=sf-dump-note>Vault Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Vault Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003610165\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.018557, "xdebug_link": null}, {"message": "[\n  ability => Vault Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-317798387 data-indent-pad=\"  \"><span class=sf-dump-note>Vault Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Vault Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317798387\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.020079, "xdebug_link": null}, {"message": "[\n  ability => Vault Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1338943979 data-indent-pad=\"  \"><span class=sf-dump-note>Vault Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Vault Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1338943979\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.021536, "xdebug_link": null}, {"message": "[\n  ability => Attendance Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-7113127 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7113127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.024796, "xdebug_link": null}, {"message": "[\n  ability => Attendance Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-60826703 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60826703\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.026297, "xdebug_link": null}, {"message": "[\n  ability => Attendance Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-987017785 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Attendance Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987017785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.027851, "xdebug_link": null}, {"message": "[\n  ability => Attendance Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1943994913 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Attendance Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1943994913\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.032521, "xdebug_link": null}, {"message": "[\n  ability => Attendance Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1529933469 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529933469\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.034358, "xdebug_link": null}, {"message": "[\n  ability => Attendance Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-378922807 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Attendance Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-378922807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.036517, "xdebug_link": null}, {"message": "[\n  ability => Attendance Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-351793921 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351793921\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.038176, "xdebug_link": null}, {"message": "[\n  ability => Attendance Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-563091733 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Attendance Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-563091733\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.039463, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1279998793 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279998793\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.041538, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1499516144 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499516144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.042317, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1436242743 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Daily Break Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436242743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.043175, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-728420291 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728420291\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.044003, "xdebug_link": null}, {"message": "[\n  ability => Leave History Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-576984762 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Leave History Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-576984762\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.048572, "xdebug_link": null}, {"message": "[\n  ability => Leave History Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1087821403 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Leave History Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087821403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.050572, "xdebug_link": null}, {"message": "[\n  ability => Leave History Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1920067097 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Leave History Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1920067097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.053605, "xdebug_link": null}, {"message": "[\n  ability => Leave History Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1864628966 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Leave History Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864628966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.056714, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-790187510 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-790187510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.061263, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-218334820 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218334820\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.065356, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-43943224 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43943224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.068647, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1561219936 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1561219936\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.071626, "xdebug_link": null}, {"message": "[\n  ability => Announcement Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-751267724 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Announcement Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-751267724\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.074395, "xdebug_link": null}, {"message": "[\n  ability => Announcement Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-696269319 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Announcement Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696269319\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.075003, "xdebug_link": null}, {"message": "[\n  ability => Announcement Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-67431823 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Announcement Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67431823\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.075939, "xdebug_link": null}, {"message": "[\n  ability => Announcement Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-145131323 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Announcement Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-145131323\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.076608, "xdebug_link": null}, {"message": "[\n  ability => Task Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2104255167 data-indent-pad=\"  \"><span class=sf-dump-note>Task Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Task Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2104255167\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.080675, "xdebug_link": null}, {"message": "[\n  ability => Task Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-747268793 data-indent-pad=\"  \"><span class=sf-dump-note>Task Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Task Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747268793\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.082484, "xdebug_link": null}, {"message": "[\n  ability => Task Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1179447948 data-indent-pad=\"  \"><span class=sf-dump-note>Task Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Task Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179447948\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.083764, "xdebug_link": null}, {"message": "[\n  ability => Task Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1920274344 data-indent-pad=\"  \"><span class=sf-dump-note>Task Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Task Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1920274344\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.084702, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-232179252 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232179252\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.086594, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1232272305 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232272305\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.087521, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-169311700 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169311700\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.088107, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Delete,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1302341988 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1302341988\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.088682, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-517423370 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Daily Work Update Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517423370\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.089657, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-151123280 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151123280\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.090553, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2132632050 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">IT Ticket Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132632050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092395, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-637642162 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">IT Ticket Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637642162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093176, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1666726191 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">IT Ticket Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666726191\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093962, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1205830579 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">IT Ticket Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205830579\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095173, "xdebug_link": null}, {"message": "[\n  ability => Dining Room Booking Everything,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1582214281 data-indent-pad=\"  \"><span class=sf-dump-note>Dining Room Booking Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Dining Room Booking Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582214281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.100634, "xdebug_link": null}, {"message": "[\n  ability => Dining Room Booking Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-722696656 data-indent-pad=\"  \"><span class=sf-dump-note>Dining Room Booking Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Dining Room Booking Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722696656\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101744, "xdebug_link": null}, {"message": "[\n  ability => Dining Room Booking Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-636385903 data-indent-pad=\"  \"><span class=sf-dump-note>Dining Room Booking Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Dining Room Booking Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636385903\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.102918, "xdebug_link": null}, {"message": "[\n  ability => Salary Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1926723401 data-indent-pad=\"  \"><span class=sf-dump-note>Salary Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Salary Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926723401\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.104834, "xdebug_link": null}, {"message": "[\n  ability => Income Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-765355409 data-indent-pad=\"  \"><span class=sf-dump-note>Income Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Income Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765355409\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.107073, "xdebug_link": null}, {"message": "[\n  ability => Income Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2135516208 data-indent-pad=\"  \"><span class=sf-dump-note>Income Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Income Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135516208\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.108672, "xdebug_link": null}, {"message": "[\n  ability => Income Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1878874688 data-indent-pad=\"  \"><span class=sf-dump-note>Income Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Income Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1878874688\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.109678, "xdebug_link": null}, {"message": "[\n  ability => Income Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1602529437 data-indent-pad=\"  \"><span class=sf-dump-note>Income Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Income Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602529437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.110655, "xdebug_link": null}, {"message": "[\n  ability => Expense Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1601973363 data-indent-pad=\"  \"><span class=sf-dump-note>Expense Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Expense Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601973363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.111666, "xdebug_link": null}, {"message": "[\n  ability => Expense Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1094934048 data-indent-pad=\"  \"><span class=sf-dump-note>Expense Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Expense Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094934048\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.116482, "xdebug_link": null}, {"message": "[\n  ability => Expense Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-818040004 data-indent-pad=\"  \"><span class=sf-dump-note>Expense Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Expense Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818040004\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.119267, "xdebug_link": null}, {"message": "[\n  ability => Logs Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-14052369 data-indent-pad=\"  \"><span class=sf-dump-note>Logs Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Logs Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14052369\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122356, "xdebug_link": null}, {"message": "[\n  ability => App Setting Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-751093127 data-indent-pad=\"  \"><span class=sf-dump-note>App Setting Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App Setting Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-751093127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.127143, "xdebug_link": null}, {"message": "[\n  ability => App Setting Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-923328502 data-indent-pad=\"  \"><span class=sf-dump-note>App Setting Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App Setting Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-923328502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.128673, "xdebug_link": null}, {"message": "[\n  ability => App Setting Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1243446221 data-indent-pad=\"  \"><span class=sf-dump-note>App Setting Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App Setting Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243446221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.131204, "xdebug_link": null}, {"message": "[\n  ability => Weekend Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2126220174 data-indent-pad=\"  \"><span class=sf-dump-note>Weekend Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Weekend Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126220174\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.13333, "xdebug_link": null}, {"message": "[\n  ability => Holiday Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1422570622 data-indent-pad=\"  \"><span class=sf-dump-note>Holiday Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Holiday Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1422570622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.134566, "xdebug_link": null}, {"message": "[\n  ability => User Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-74820677 data-indent-pad=\"  \"><span class=sf-dump-note>User Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">User Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74820677\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.135578, "xdebug_link": null}, {"message": "[\n  ability => User Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1103721403 data-indent-pad=\"  \"><span class=sf-dump-note>User Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">User Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103721403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.13658, "xdebug_link": null}, {"message": "[\n  ability => User Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-502781292 data-indent-pad=\"  \"><span class=sf-dump-note>User Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">User Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502781292\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.137577, "xdebug_link": null}, {"message": "[\n  ability => User Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-945234324 data-indent-pad=\"  \"><span class=sf-dump-note>User Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">User Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945234324\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.139081, "xdebug_link": null}, {"message": "[\n  ability => Permission Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-934714881 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Permission Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934714881\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.140049, "xdebug_link": null}, {"message": "[\n  ability => Role Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-684902771 data-indent-pad=\"  \"><span class=sf-dump-note>Role Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Role Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-684902771\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.140899, "xdebug_link": null}, {"message": "[\n  ability => Role Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-876547101 data-indent-pad=\"  \"><span class=sf-dump-note>Role Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Role Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876547101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.141721, "xdebug_link": null}, {"message": "[\n  ability => Role Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1841323872 data-indent-pad=\"  \"><span class=sf-dump-note>Role Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Role Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841323872\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.142563, "xdebug_link": null}, {"message": "[\n  ability => Permission Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-614514245 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Permission Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614514245\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.14344, "xdebug_link": null}, {"message": "[\n  ability => Permission Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-327117389 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Permission Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327117389\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.144308, "xdebug_link": null}, {"message": "[\n  ability => Permission Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2027616238 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Permission Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027616238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.146, "xdebug_link": null}]}, "session": {"_token": "VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/announcement/all\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1749549177\n]", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/announcement/show/w0m8nb5By7WPvjad", "action_name": "administration.announcement.show", "controller_action": "App\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController@show", "uri": "GET announcement/show/{announcement}", "controller": "App\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController@show<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=146\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/announcement", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAnnouncement%2FAnnouncementController.php&line=146\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Announcement/AnnouncementController.php:146-189</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Announcement Read", "duration": "7.16s", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-472022043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-472022043\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-924895192 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-924895192\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1981317322 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">https://blueorange.test/announcement/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6Ik5OR3J2YWc3NkRqVlc3VGdPTXh3dEE9PSIsInZhbHVlIjoiWDd2MEI3dGw3ZStrSHRjdG1uaDY1Q3l5ajFoV1FXU1g5c0xIeUxHOVhKSXk1ZEozUUtUeVplaFhDM3pXVmFnMndqZEYrYk9DSW5UR0pOYXpxTnpLL3RsSVd1ZDdsdGg4cHVjMEVIcjJiMXFDVTBtWXUxU3phc3BnaUs0eGFnNU4iLCJtYWMiOiIyOWQwN2Y2ZDY4NGQxMGQyYjJiZGRiZWZkZGQ4N2VmNjQ5MjZiMGUxYWMyNmU1Y2I0OTQ4NWM1MzdlNmZlNDIxIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6ImR6eHlaMDNiQ1Fsc0QvdTl0RDFGMUE9PSIsInZhbHVlIjoiUXpZTGlKYlRoVzBkanh4Q25VK1ArKzBQWVNpM2I5WnpYZ3Z2TFpkeGIvMWNLU3lUYXplZjFyd2ZocG0zbUNXRmt0SWFDMTlDUjIyNGRoUkdERTZoTXRrekhUR3ZOUVo0dFJZVDhuTmhveXdzUWZkQnVkeU1KVWpObTR0Y3NxdnkiLCJtYWMiOiJmNzkyYjYzNWNlYzNjMWQzNWEzYjc3YzNmZjBhZWUwMzU5MTBlNTAwZTcxMzNjY2VlZTgyNmEwNDEyYjc5OWE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1981317322\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-158332380 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dFIY5PGHO9DJ7O3P9VP8I9MtkVpL8ReySUApdhKj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-158332380\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1146058560 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 12 Jun 2025 11:25:22 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146058560\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2147232805 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">https://blueorange.test/announcement/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749549177</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2147232805\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/announcement/show/w0m8nb5By7WPvjad", "action_name": "administration.announcement.show", "controller_action": "App\\Http\\Controllers\\Administration\\Announcement\\AnnouncementController@show"}, "badge": null}}