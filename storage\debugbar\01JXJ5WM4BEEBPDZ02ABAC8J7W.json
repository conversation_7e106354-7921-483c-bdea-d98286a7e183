{"__meta": {"id": "01JXJ5WM4BEEBPDZ02ABAC8J7W", "datetime": "2025-06-12 19:39:52", "utime": **********.077106, "method": "POST", "uri": "/attendance/clockin", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749735585.466965, "end": **********.077129, "duration": 6.610163927078247, "duration_str": "6.61s", "measures": [{"label": "Booting", "start": 1749735585.466965, "relative_start": 0, "end": **********.759526, "relative_end": **********.759526, "duration": 1.****************, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.759552, "relative_start": 1.****************, "end": **********.077132, "relative_end": 3.0994415283203125e-06, "duration": 5.***************, "duration_str": "5.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.794744, "relative_start": 1.****************, "end": **********.802069, "relative_end": **********.802069, "duration": 0.007324934005737305, "duration_str": "7.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.072814, "relative_start": 6.***************, "end": **********.073456, "relative_end": **********.073456, "duration": 0.0006420612335205078, "duration_str": "642μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST attendance/clockin", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Attendance Read", "controller": "App\\Http\\Controllers\\Administration\\Attendance\\AttendanceController@clockIn<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=219\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.attendance.clockin", "prefix": "/attendance", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=219\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Attendance/AttendanceController.php:219-235</a>"}, "queries": {"count": 18, "nb_statements": 16, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05400999999999999, "accumulated_duration_str": "54.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.916137, "duration": 0.005900000000000001, "duration_str": "5.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 10.924}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.936368, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 10.924, "width_percent": 1.463}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9852371, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 12.387, "width_percent": 4.221}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.99279, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "blueorange", "explain": null, "start_percent": 16.608, "width_percent": 3.777}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 222}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.0153632, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "AttendanceController.php:50", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=50", "ajax": false, "filename": "AttendanceController.php", "line": "50"}, "connection": "blueorange", "explain": null, "start_percent": 20.385, "width_percent": 4.74}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 222}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.021717, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "AttendanceController.php:50", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=50", "ajax": false, "filename": "AttendanceController.php", "line": "50"}, "connection": "blueorange", "explain": null, "start_percent": 25.125, "width_percent": 2.222}, {"sql": "select * from `employees` where `employees`.`user_id` in (1) and `employees`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 222}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.037708, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "AttendanceController.php:50", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=50", "ajax": false, "filename": "AttendanceController.php", "line": "50"}, "connection": "blueorange", "explain": null, "start_percent": 27.347, "width_percent": 4.407}, {"sql": "select * from `shortcuts` where `shortcuts`.`user_id` in (1) and `shortcuts`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 222}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.046589, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "AttendanceController.php:50", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=50", "ajax": false, "filename": "AttendanceController.php", "line": "50"}, "connection": "blueorange", "explain": null, "start_percent": 31.753, "width_percent": 3.685}, {"sql": "select `id`, `model_id`, `model_type`, `disk`, `collection_name`, `file_name` from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 222}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.053262, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "AttendanceController.php:50", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=50", "ajax": false, "filename": "AttendanceController.php", "line": "50"}, "connection": "blueorange", "explain": null, "start_percent": 35.438, "width_percent": 3.277}, {"sql": "select * from `employee_shifts` where `employee_shifts`.`user_id` in (1) and `status` = 'Active' and `employee_shifts`.`deleted_at` is null order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["Active"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 222}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.060008, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "AttendanceController.php:50", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=50", "ajax": false, "filename": "AttendanceController.php", "line": "50"}, "connection": "blueorange", "explain": null, "start_percent": 38.715, "width_percent": 5.092}, {"sql": "select * from `attendances` where `user_id` = 1 and `clock_out` is null and `attendances`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 32}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 227}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1267788, "duration": 0.006, "duration_str": "6ms", "memory": 0, "memory_str": null, "filename": "AttendanceEntryService.php:32", "source": {"index": 16, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FAttendance%2FAttendanceEntryService.php&line=32", "ajax": false, "filename": "AttendanceEntryService.php", "line": "32"}, "connection": "blueorange", "explain": null, "start_percent": 43.807, "width_percent": 11.109}, {"sql": "select exists(select * from `weekends` where `day` = 'Thursday' and `is_active` = 1 and `weekends`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["Thursday", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 201}, {"index": 12, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 39}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 227}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.13799, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "AttendanceEntryService.php:201", "source": {"index": 11, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FAttendance%2FAttendanceEntryService.php&line=201", "ajax": false, "filename": "AttendanceEntryService.php", "line": "201"}, "connection": "blueorange", "explain": null, "start_percent": 54.916, "width_percent": 2.111}, {"sql": "select exists(select * from `holidays` where `date` = '2025-06-12' and `is_active` = 1 and `holidays`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["2025-06-12", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 207}, {"index": 12, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 43}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 227}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1446369, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "AttendanceEntryService.php:207", "source": {"index": 11, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FAttendance%2FAttendanceEntryService.php&line=207", "ajax": false, "filename": "AttendanceEntryService.php", "line": "207"}, "connection": "blueorange", "explain": null, "start_percent": 57.026, "width_percent": 2.314}, {"sql": "select * from `attendances` where `user_id` = 1 and `clock_in_date` = '2025-06-12' and `type` = 'Regular' and `attendances`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, "2025-06-12", "Regular"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 51}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 227}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.150253, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "AttendanceEntryService.php:51", "source": {"index": 16, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FAttendance%2FAttendanceEntryService.php&line=51", "ajax": false, "filename": "AttendanceEntryService.php", "line": "51"}, "connection": "blueorange", "explain": null, "start_percent": 59.341, "width_percent": 3.111}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 59}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 227}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.025793, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "AttendanceEntryService.php:59", "source": {"index": 10, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FAttendance%2FAttendanceEntryService.php&line=59", "ajax": false, "filename": "AttendanceEntryService.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 62.451, "width_percent": 0}, {"sql": "select * from `employee_shifts` where `employee_shifts`.`user_id` = 1 and `employee_shifts`.`user_id` is not null and `status` = 'Active' and `employee_shifts`.`deleted_at` is null order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [1, "Active"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 64}, {"index": 23, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 59}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 227}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.026342, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "AttendanceEntryService.php:64", "source": {"index": 19, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FAttendance%2FAttendanceEntryService.php&line=64", "ajax": false, "filename": "AttendanceEntryService.php", "line": "64"}, "connection": "blueorange", "explain": null, "start_percent": 62.451, "width_percent": 2.592}, {"sql": "insert into `attendances` (`user_id`, `employee_shift_id`, `clock_in_date`, `clock_in`, `type`, `clockin_medium`, `clockin_scanner_id`, `ip_address`, `country`, `city`, `zip_code`, `time_zone`, `latitude`, `longitude`, `updated_at`, `created_at`) values (1, 1, '2025-06-12', '2025-06-12 19:39:47', 'Regular', 'Manual', null, '**************', 'Bangladesh', 'Dhaka', '1208', 'Asia/Dhaka', '23.7804', '90.4061', '2025-06-12 19:39:52', '2025-06-12 19:39:52')", "type": "query", "params": [], "bindings": [1, 1, "2025-06-12", "2025-06-12 19:39:47", "Regular", "Manual", null, "**************", "Bangladesh", "Dhaka", "1208", "Asia/Dhaka", "23.7804", "90.4061", "2025-06-12 19:39:52", "2025-06-12 19:39:52"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 70}, {"index": 25, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 59}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 227}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.030761, "duration": 0.018879999999999997, "duration_str": "18.88ms", "memory": 0, "memory_str": null, "filename": "AttendanceEntryService.php:70", "source": {"index": 21, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FAttendance%2FAttendanceEntryService.php&line=70", "ajax": false, "filename": "AttendanceEntryService.php", "line": "70"}, "connection": "blueorange", "explain": null, "start_percent": 65.044, "width_percent": 34.956}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 59}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Administration/Attendance/AttendanceController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Attendance\\AttendanceController.php", "line": 227}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.056646, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "AttendanceEntryService.php:59", "source": {"index": 9, "namespace": null, "name": "app/Services/Administration/Attendance/AttendanceEntryService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Attendance\\AttendanceEntryService.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FAttendance%2FAttendanceEntryService.php&line=59", "ajax": false, "filename": "AttendanceEntryService.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\EmployeeShift\\EmployeeShift": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FEmployeeShift%2FEmployeeShift.php&line=1", "ajax": false, "filename": "EmployeeShift.php", "line": "?"}}, "App\\Models\\Settings\\Settings": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "App\\Models\\User\\Employee\\Employee": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FEmployee%2FEmployee.php&line=1", "ajax": false, "filename": "Employee.php", "line": "?"}}}, "count": 8, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => Attendance Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1303975313 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Attendance Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303975313\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.009825, "xdebug_link": null}]}, "session": {"_token": "VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/dashboard/calendar/events?end=2025-07-12T00%3A00%3A00%2B06%3A00&start=2025-05-31T00%3A00%3A00%2B06%3A00\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:13 [\n    0 => \"alert.config.title\"\n    1 => \"alert.config.text\"\n    2 => \"alert.config.timer\"\n    3 => \"alert.config.width\"\n    4 => \"alert.config.padding\"\n    5 => \"alert.config.showConfirmButton\"\n    6 => \"alert.config.showCloseButton\"\n    7 => \"alert.config.timerProgressBar\"\n    8 => \"alert.config.customClass\"\n    9 => \"alert.config.toast\"\n    10 => \"alert.config.icon\"\n    11 => \"alert.config.position\"\n    12 => \"alert.config\"\n  ]\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1749549177\n]", "alert": "array:1 [\n  \"config\" => \"{\"title\":\"Clocked In Successful.\",\"text\":\"\",\"timer\":\"5000\",\"width\":\"32rem\",\"padding\":\"1.25rem\",\"showConfirmButton\":false,\"showCloseButton\":true,\"timerProgressBar\":true,\"customClass\":{\"container\":null,\"popup\":null,\"header\":null,\"title\":null,\"closeButton\":null,\"icon\":null,\"image\":null,\"content\":null,\"input\":null,\"actions\":null,\"confirmButton\":null,\"cancelButton\":null,\"footer\":null},\"toast\":true,\"icon\":\"success\",\"position\":\"top-end\"}\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "https://blueorange.test/attendance/clockin", "action_name": "administration.attendance.clockin", "controller_action": "App\\Http\\Controllers\\Administration\\Attendance\\AttendanceController@clockIn", "uri": "POST attendance/clockin", "controller": "App\\Http\\Controllers\\Administration\\Attendance\\AttendanceController@clockIn<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=219\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/attendance", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FAttendance%2FAttendanceController.php&line=219\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Attendance/AttendanceController.php:219-235</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Attendance Read", "duration": "6.61s", "peak_memory": "30MB", "response": "Redirect to https://blueorange.test/dashboard", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-882865094 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-882865094\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-245182927 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D</span>\"\n  \"<span class=sf-dump-key>attendance</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Regular</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-245182927\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1931175379 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">66</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">https://blueorange.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6InFGOXREVElNZ1dnZ0xkNnE5RzE5cWc9PSIsInZhbHVlIjoicVlwYjdBLzUzaUlySndHaS83ODE1Y2xwSnNCS1FRcWNBUDVCS2EveUFoS0ZadmV5Q0ROZXB1UFgwNk5ROUFtWGhKZEwxN1U0MjhsRkJZb0I1TitIN2FGaVVnUVlFU2tJSzlJYW9OdXpGbmR1SldzNXF2SVdvRWtkQ09jVkU1YmQiLCJtYWMiOiIyZTU4YTg5ZDhjOTFmODVhMzgwZGFhNmRmZWEyZmQ3Zjc4NzM3NzEwOWQ0YWI0OGMxMTA3NWUxZjRlYTQ4NmFhIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6ImJHdmNPbkluQ2NZUW5KQy8vSTlwV0E9PSIsInZhbHVlIjoiT2V1bllkZG4wZjBSWnR5YU50WGVNNDlEdGJ0aC9qODVyaVBtUkNWd3prMXBSdE9sSE4yL2JFcFRhZFNMcm5odHVvcGl1VC9ETDdhZytPNTNYeEVBTVA1ZHRQemhrQWFvNzBldGpOM3NkcDJkQXNlc3g0M0hqdmRxSENzTlZrdnciLCJtYWMiOiIzZGMwZGQ1NWY4YWExNDkyMDM2OGQwOGEwZTQ2NTFlZDRkYmUwZGE3YzRlNWFjZDMwMjkzMWE1Zjk5MTUyNTljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931175379\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1082251043 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dFIY5PGHO9DJ7O3P9VP8I9MtkVpL8ReySUApdhKj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082251043\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1282280101 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 12 Jun 2025 13:39:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">https://blueorange.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282280101\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-217836136 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqGRS6hU5aXdSrGUwxpPGAvdjSbiYghuFHpNew2D</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"127 characters\">https://blueorange.test/dashboard/calendar/events?end=2025-07-12T00%3A00%3A00%2B06%3A00&amp;start=2025-05-31T00%3A00%3A00%2B06%3A00</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.text</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.timer</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.width</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"20 characters\">alert.config.padding</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.showConfirmButton</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"28 characters\">alert.config.showCloseButton</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.timerProgressBar</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"24 characters\">alert.config.customClass</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.toast</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.icon</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"21 characters\">alert.config.position</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"12 characters\">alert.config</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749549177</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"434 characters\">{&quot;title&quot;:&quot;Clocked In Successful.&quot;,&quot;text&quot;:&quot;&quot;,&quot;timer&quot;:&quot;5000&quot;,&quot;width&quot;:&quot;32rem&quot;,&quot;padding&quot;:&quot;1.25rem&quot;,&quot;showConfirmButton&quot;:false,&quot;showCloseButton&quot;:true,&quot;timerProgressBar&quot;:true,&quot;customClass&quot;:{&quot;container&quot;:null,&quot;popup&quot;:null,&quot;header&quot;:null,&quot;title&quot;:null,&quot;closeButton&quot;:null,&quot;icon&quot;:null,&quot;image&quot;:null,&quot;content&quot;:null,&quot;input&quot;:null,&quot;actions&quot;:null,&quot;confirmButton&quot;:null,&quot;cancelButton&quot;:null,&quot;footer&quot;:null},&quot;toast&quot;:true,&quot;icon&quot;:&quot;success&quot;,&quot;position&quot;:&quot;top-end&quot;}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-217836136\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://blueorange.test/attendance/clockin", "action_name": "administration.attendance.clockin", "controller_action": "App\\Http\\Controllers\\Administration\\Attendance\\AttendanceController@clockIn"}, "badge": "302 Found"}}