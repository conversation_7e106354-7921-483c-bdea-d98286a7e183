1749735678O:55:"Illuminate\Notifications\DatabaseNotificationCollection":2:{s:8:" * items";a:1:{i:0;O:45:"Illuminate\Notifications\DatabaseNotification":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"notifications";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:6:"string";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";s:36:"97e5be29-c77c-4827-9f51-372878d5eff8";s:4:"type";s:67:"App\Notifications\Administration\Penalty\PenaltyCreatedNotification";s:15:"notifiable_type";s:15:"App\Models\User";s:13:"notifiable_id";i:1;s:4:"data";s:193:"{"url":"https:\/\/blueorange.test\/penalty\/show\/yGnJVK564xeXWPpB","icon":"gavel","title":"Penalty Assigned","message":"A penalty (Dress Code Violation) has been assigned to you by Developer"}";s:7:"read_at";N;s:10:"created_at";s:19:"2025-06-12 19:40:35";s:10:"updated_at";s:19:"2025-06-12 19:40:35";}s:11:" * original";a:8:{s:2:"id";s:36:"97e5be29-c77c-4827-9f51-372878d5eff8";s:4:"type";s:67:"App\Notifications\Administration\Penalty\PenaltyCreatedNotification";s:15:"notifiable_type";s:15:"App\Models\User";s:13:"notifiable_id";i:1;s:4:"data";s:193:"{"url":"https:\/\/blueorange.test\/penalty\/show\/yGnJVK564xeXWPpB","icon":"gavel","title":"Penalty Assigned","message":"A penalty (Dress Code Violation) has been assigned to you by Developer"}";s:7:"read_at";N;s:10:"created_at";s:19:"2025-06-12 19:40:35";s:10:"updated_at";s:19:"2025-06-12 19:40:35";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:4:"data";s:5:"array";s:7:"read_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}