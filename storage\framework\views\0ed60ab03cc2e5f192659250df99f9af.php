<div class="col-md-4">
    <div class="card card-action card-border-shadow-danger mb-1">
        <div class="card-header collapsed">
            <div class="card-action-title"><?php echo e(__('Absent Today')); ?></div>
            <div class="card-action-element">
                <ul class="list-inline mb-0">
                    <li class="list-inline-item">
                        <a href="javascript:void(0);" class="card-collapsible">
                            <i class="tf-icons ti ti-chevron-right scaleX-n1-rtl ti-sm"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="collapse">
            <div class="card-body pt-0">
                <div class="d-flex align-items-center flex-wrap">
                    <?php $__empty_1 = true; $__currentLoopData = $absentUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $absentUser): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="avatar me-2 mb-2 border border-3 rounded border-danger" title="<?php echo e($absentUser->employee->alias_name ?? $absentUser->name); ?>">
                            <?php if($absentUser->getFirstMediaUrl('avatar')): ?>
                                <img src="<?php echo e($absentUser->getFirstMediaUrl('avatar', 'thumb')); ?>" alt="<?php echo e($absentUser->name); ?>" class="rounded" />
                            <?php else: ?>
                                <span class="avatar-initial rounded bg-label-danger"><?php echo e(substr($absentUser->name, 0, 1)); ?></span>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center w-100 py-3">
                            <p class="mb-0 text-muted">No absent users today</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/dashboard/partials/_absent_today.blade.php ENDPATH**/ ?>