<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Announcement')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All Announcements')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Announcement')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('All Announcements')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">All Announcements</h5>

                <div class="card-header-elements ms-auto">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Announcement Create')): ?>
                        <a href="<?php echo e(route('administration.announcement.create')); ?>" class="btn btn-sm btn-primary">
                            <span class="tf-icon ti ti-plus ti-xs me-1"></span>
                            Create Announcement
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive-md table-responsive-sm w-100">
                    <table class="table data-table table-bordered">
                        <thead>
                            <tr>
                                <th>Sl.</th>
                                <th>Announced At</th>
                                <th>Announcement</th>
                                <th>Announcer</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $announcements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $announcement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <th>#<?php echo e(serial($announcements, $key)); ?></th>
                                    <td><?php echo e(show_date($announcement->created_at)); ?></td>
                                    <td>
                                        <b><?php echo e($announcement->title); ?></b>
                                        <br>
                                        <?php if(!is_null($announcement->recipients)): ?>
                                            <small class="text-primary text-bold cursor-pointer text-left" title="
                                                <?php $__currentLoopData = $announcement->recipients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $recipient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if($index < 9): ?>
                                                        <small><?php echo e(show_employee_data($recipient, 'alias_name')); ?></small>
                                                        <br>
                                                    <?php elseif($index == 9): ?>
                                                        <?php
                                                            $remainingCount = count($announcement->recipients) - 9;
                                                        ?>
                                                        <?php echo e($remainingCount); ?> More
                                                        <?php break; ?>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            ">
                                                <?php echo e(count($announcement->recipients)); ?> Recipients
                                            </small>
                                        <?php else: ?>
                                            <small class="text-muted">All Recipients</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo show_user_name_and_avatar($announcement->announcer, name: null); ?>

                                    </td>
                                    <td class="text-center">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Announcement Delete')): ?>
                                            <a href="<?php echo e(route('administration.announcement.destroy', ['announcement' => $announcement])); ?>" class="btn btn-sm btn-icon btn-danger confirm-danger" data-bs-toggle="tooltip" title="Delete Announcement?">
                                                <i class="text-white ti ti-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Announcement Update')): ?>
                                            <a href="<?php echo e(route('administration.announcement.edit', ['announcement' => $announcement])); ?>" class="btn btn-sm btn-icon btn-info" data-bs-toggle="tooltip" title="Edit Announcement?">
                                                <i class="text-white ti ti-pencil"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Announcement Read')): ?>
                                            <a href="<?php echo e(route('administration.announcement.show', ['announcement' => $announcement])); ?>" class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip" title="Show Details">
                                                <i class="text-white ti ti-info-hexagon"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/announcement/my.blade.php ENDPATH**/ ?>